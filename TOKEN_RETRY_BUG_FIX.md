# 🔧 Token过期重试机制Bug修复

## 问题描述

### 错误日志
```
2025-08-01 16:15:12.146 [http-nio-18088-exec-3] WARN c.z.service.FeishuBitableService - 检测到token过期，抛出异常以触发重试
2025-08-01 16:15:12.146 [http-nio-18088-exec-3] ERROR c.z.c.FeishuBitableController - 字段映射图片上传异常
com.ziniao.exception.TokenExpiredException: Token已过期
at com.ziniao.service.FeishuBitableService.getRecordsInternal(FeishuBitableService.java:262)
```

### 问题分析

1. **FeishuBitableService重试机制不完善**
   - `executeWithTokenRetry`方法只重试一次
   - 没有处理包装的TokenExpiredException
   - 缺少重试次数限制和延迟机制

2. **Controller层异常处理不当**
   - 简单地返回500错误，没有区分token错误
   - 客户端无法识别是否应该重试

3. **ClothingService重试机制脆弱**
   - token刷新失败时没有适当的错误处理
   - 缺少详细的错误日志

## 修复方案

### 1. 改进FeishuBitableService重试机制

#### 主要改进
- ✅ **增加重试次数限制**：最多重试2次，避免无限循环
- ✅ **处理包装异常**：检测RuntimeException中的TokenExpiredException
- ✅ **递增延迟机制**：避免频繁重试造成系统压力
- ✅ **详细错误日志**：记录每次重试的详细信息
- ✅ **异常链保持**：保持原始异常信息

#### 核心代码
```java
private <T> T executeWithTokenRetry(Supplier<T> operation) throws Exception {
    int maxRetries = 2; // 最多重试2次
    int retryCount = 0;
    
    while (retryCount <= maxRetries) {
        try {
            return operation.get();
        } catch (TokenExpiredException e) {
            retryCount++;
            if (retryCount > maxRetries) {
                logger.error("Token重试次数已达上限 {}, 放弃重试: {}", maxRetries, e.getMessage());
                throw new Exception("Token过期重试失败，已达最大重试次数: " + maxRetries, e);
            }
            
            logger.warn("Token过期，第{}次尝试刷新token并重试: {}", retryCount, e.getMessage());

            // 强制刷新token
            try {
                tokenService.forceRefreshAppAccessToken();
                logger.info("Token已刷新，第{}次重试操作", retryCount);
            } catch (Exception refreshException) {
                logger.error("刷新token失败: {}", refreshException.getMessage());
                throw new Exception("刷新token失败", refreshException);
            }

            // 添加短暂延迟避免频繁重试
            if (retryCount > 1) {
                try {
                    Thread.sleep(1000 * retryCount); // 递增延迟
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new Exception("重试被中断", ie);
                }
            }
            
            // 继续下一次循环重试
            continue;
            
        } catch (RuntimeException e) {
            // 检查是否是包装的TokenExpiredException
            if (e.getCause() instanceof TokenExpiredException) {
                // 同样的重试逻辑
            }
            
            // 其他异常处理
            if (e.getCause() instanceof Exception) {
                throw (Exception) e.getCause();
            }
            throw e;
        }
    }
    
    throw new Exception("未知错误：重试循环异常退出");
}
```

### 2. 改进Controller层异常处理

#### 主要改进
- ✅ **Token错误识别**：专门检测token相关异常
- ✅ **返回适当状态码**：token错误返回401而不是500
- ✅ **客户端友好消息**：提示客户端可以重试

#### 核心代码
```java
} catch (Exception e) {
    logger.error("字段映射图片上传异常", e);
    
    // 检查是否是token相关异常
    if (isTokenRelatedError(e)) {
        logger.warn("检测到token相关异常，建议客户端重试: {}", e.getMessage());
        return ResponseEntity.status(401).body(
                FeishuImageDownloadResponse.error(401, "认证失败，token可能已过期，请重试: " + e.getMessage()));
    }
    
    return ResponseEntity.status(500).body(
            FeishuImageDownloadResponse.error(500, "服务器内部错误: " + e.getMessage()));
}

private boolean isTokenRelatedError(Exception e) {
    if (e == null) return false;

    String errorMessage = e.getMessage();
    if (errorMessage != null) {
        String lowerMsg = errorMessage.toLowerCase();
        // 检查token相关关键词
        if (lowerMsg.contains("token") && (
                lowerMsg.contains("expired") ||
                lowerMsg.contains("过期") ||
                lowerMsg.contains("invalid") ||
                lowerMsg.contains("unauthorized") ||
                lowerMsg.contains("认证失败") ||
                lowerMsg.contains("授权权限不足")
        )) {
            return true;
        }
        
        // 检查飞书特定错误码
        if (lowerMsg.contains("99991661") || 
            lowerMsg.contains("99991663") || 
            lowerMsg.contains("99991664")) {
            return true;
        }
    }

    // 检查异常类型
    if (e.getClass().getSimpleName().contains("TokenExpired")) {
        return true;
    }

    // 检查cause异常
    Throwable cause = e.getCause();
    if (cause != null && cause.getClass().getSimpleName().contains("TokenExpired")) {
        return true;
    }

    return false;
}
```

### 3. 改进ClothingService重试机制

#### 主要改进
- ✅ **异常处理增强**：token刷新失败时的错误处理
- ✅ **详细日志记录**：记录重试过程的详细信息
- ✅ **二次验证**：重试后再次检查是否仍然是token错误

#### 核心代码
```java
// 检查是否是令牌过期错误，如果是则重试一次
if (!response.isSuccess() && isTokenExpiredError(response)) {
    logger.warn("检测到令牌过期错误，尝试刷新令牌并重试: {}", response.getErrorMsg());

    try {
        // 强制刷新令牌
        String newToken = tokenService.forceRefreshToken();
        logger.info("令牌已刷新，使用新令牌重试API调用");

        // 更新curl命令中的令牌并重新打印
        printCurlCommand(apiPath, bizContent, newToken);

        // 使用新令牌重试
        response = getOpenClient().executeAppToken(apiRequest, newToken);
        
        // 如果重试后仍然失败且是token错误，记录详细信息
        if (!response.isSuccess() && isTokenExpiredError(response)) {
            logger.error("令牌刷新后重试仍然失败，可能存在令牌服务问题: {}", response.getErrorMsg());
        }
    } catch (Exception tokenRefreshException) {
        logger.error("刷新令牌失败: {}", tokenRefreshException.getMessage());
        // 继续使用原始响应，让后续逻辑处理
    }
}
```

### 4. 改进TokenService错误处理

#### 主要改进
- ✅ **缓存清理**：获取失败时清理可能损坏的缓存
- ✅ **详细日志**：记录token刷新的详细过程
- ✅ **异常传播**：保持异常信息的完整性

## 测试验证

### 单元测试
创建了`TokenRetryTest`类，测试以下场景：
- ✅ Token过期后成功重试
- ✅ 达到最大重试次数后失败
- ✅ Token刷新失败的处理
- ✅ 包装异常的处理

### 测试用例
```java
@Test
void testTokenRetrySuccess() throws Exception {
    // 模拟第一次调用失败，第二次成功
    Supplier<String> operation = mock(Supplier.class);
    when(operation.get())
            .thenThrow(new TokenExpiredException("Token已过期"))
            .thenReturn("success");

    String result = bitableService.testExecuteWithTokenRetry(operation);
    assertEquals("success", result);
    verify(operation, times(2)).get();
    verify(tokenService, times(1)).forceRefreshAppAccessToken();
}
```

## 预期效果

### 1. 提高系统稳定性
- 减少因token过期导致的API调用失败
- 自动处理token刷新，无需人工干预

### 2. 改善用户体验
- 客户端收到401错误时知道可以重试
- 减少因token问题导致的业务中断

### 3. 增强可观测性
- 详细的重试日志便于问题排查
- 区分token错误和其他系统错误

### 4. 防止系统过载
- 重试次数限制避免无限循环
- 递增延迟机制减少系统压力

## 部署建议

1. **监控重试频率**：观察token重试的频率，如果过高可能需要调整token缓存策略
2. **日志级别调整**：生产环境可以将debug日志调整为info级别
3. **告警设置**：对连续的token刷新失败设置告警
4. **性能监控**：监控重试机制对API响应时间的影响

## 后续优化

1. **预防性刷新**：在token即将过期前主动刷新
2. **熔断机制**：连续失败时暂时停止调用
3. **指标收集**：收集token重试的成功率和耗时统计
4. **配置化**：将重试次数和延迟时间配置化

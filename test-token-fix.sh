#!/bin/bash

# 测试图片URL token过期修复效果
# 用于验证 /api/feishu/bitable/images/field-mapping-by-record 接口的修复

echo "🔧 测试图片URL token过期修复效果"
echo "=================================="

# 配置
BASE_URL="http://39.108.93.224:18088"
LOG_FILE="/www/wwwroot/fs.vwo50.life_998/logs/ziniao-ai-demo.log"

echo ""
echo "1. 检查应用状态"
echo "---------------"
HEALTH_STATUS=$(curl -s -w "%{http_code}" -o /dev/null "$BASE_URL/actuator/health")
if [ "$HEALTH_STATUS" = "200" ]; then
    echo "✅ 应用运行正常"
else
    echo "❌ 应用状态异常，HTTP状态码: $HEALTH_STATUS"
    exit 1
fi

echo ""
echo "2. 测试接口调用"
echo "---------------"
echo "📋 调用 /api/feishu/bitable/images/field-mapping-by-record 接口..."

# 示例请求数据（请根据实际情况修改）
REQUEST_DATA='{
  "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
  "tableId": "tbl4sH8PYHUk36K0",
  "fieldMapping": {
    "👚上装正面图": "👚上装正面图url"
  },
  "recordIds": ["recqwIwhc6"],
  "summaryOnly": false,
  "downloadTimeout": 30,
  "updateBitableWithLocalUrl": true
}'

echo "🚀 发送请求..."
RESPONSE=$(curl -s -X POST \
  -H "Content-Type: application/json" \
  -d "$REQUEST_DATA" \
  "$BASE_URL/api/feishu/bitable/images/field-mapping-by-record")

echo "📋 响应内容:"
echo "$RESPONSE" | jq '.' 2>/dev/null || echo "$RESPONSE"

echo ""
echo "3. 检查修复日志"
echo "---------------"
if [ -f "$LOG_FILE" ]; then
    echo "📋 查找最新的修复日志:"
    tail -n 500 "$LOG_FILE" | grep -E "(修复图片URL token过期问题|修复URL字段图片token过期问题)" | tail -10
    
    echo ""
    echo "📋 查找URL替换日志:"
    tail -n 500 "$LOG_FILE" | grep -E "(原始URL.*本地URL|临时URL.*本地URL)" | tail -5
else
    echo "❌ 日志文件不存在: $LOG_FILE"
fi

echo ""
echo "4. 验证响应中的URL"
echo "------------------"
echo "🔍 检查响应中的图片URL是否为本地URL..."

# 提取响应中的URL并检查
ORIGINAL_URLS=$(echo "$RESPONSE" | jq -r '.data.records[].imageDetails | to_entries[] | .value[] | .originalUrl' 2>/dev/null | grep -v null)
TMP_URLS=$(echo "$RESPONSE" | jq -r '.data.records[].imageDetails | to_entries[] | .value[] | .tmpDownloadUrl' 2>/dev/null | grep -v null)
LOCAL_URLS=$(echo "$RESPONSE" | jq -r '.data.records[].imageDetails | to_entries[] | .value[] | .localAccessUrl' 2>/dev/null | grep -v null)

echo "📋 原始URL (originalUrl):"
if [ -n "$ORIGINAL_URLS" ]; then
    echo "$ORIGINAL_URLS" | while read url; do
        if [[ "$url" == *"image-proxy"* ]] || [[ "$url" == *"uploads"* ]]; then
            echo "✅ $url (本地URL)"
        else
            echo "❌ $url (飞书URL - 可能存在token过期问题)"
        fi
    done
else
    echo "   (无数据)"
fi

echo ""
echo "📋 临时下载URL (tmpDownloadUrl):"
if [ -n "$TMP_URLS" ]; then
    echo "$TMP_URLS" | while read url; do
        if [[ "$url" == *"image-proxy"* ]] || [[ "$url" == *"uploads"* ]]; then
            echo "✅ $url (本地URL)"
        else
            echo "❌ $url (飞书URL - 可能存在token过期问题)"
        fi
    done
else
    echo "   (无数据)"
fi

echo ""
echo "📋 本地访问URL (localAccessUrl):"
if [ -n "$LOCAL_URLS" ]; then
    echo "$LOCAL_URLS" | while read url; do
        echo "✅ $url"
    done
else
    echo "   (无数据)"
fi

echo ""
echo "5. 测试URL可访问性"
echo "------------------"
if [ -n "$LOCAL_URLS" ]; then
    echo "$LOCAL_URLS" | head -3 | while read url; do
        if [ -n "$url" ]; then
            echo "🧪 测试URL: $url"
            HTTP_STATUS=$(curl -s -w "%{http_code}" -o /dev/null "$url")
            if [ "$HTTP_STATUS" = "200" ]; then
                echo "✅ URL可正常访问"
            else
                echo "❌ URL访问失败，HTTP状态码: $HTTP_STATUS"
            fi
        fi
    done
else
    echo "⚠️  没有找到本地URL进行测试"
fi

echo ""
echo "6. 总结"
echo "-------"
echo "🎯 修复目标: 确保响应中的 originalUrl 和 tmpDownloadUrl 字段使用本地URL而不是会过期的飞书token URL"
echo "📊 修复状态: 请查看上述检查结果"
echo ""
echo "💡 如果仍然看到飞书URL，请检查:"
echo "   1. 图片是否成功下载到本地"
echo "   2. downloadToLocal 参数是否为 true"
echo "   3. 应用日志中的修复日志"

echo ""
echo "测试完成！"

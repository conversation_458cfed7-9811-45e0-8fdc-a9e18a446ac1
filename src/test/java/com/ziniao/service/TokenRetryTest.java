package com.ziniao.service;

import com.ziniao.exception.TokenExpiredException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.function.Supplier;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Token重试机制测试
 */
public class TokenRetryTest {

    private static final Logger logger = LoggerFactory.getLogger(TokenRetryTest.class);

    @Mock
    private FeishuTokenService tokenService;

    private TestableFeishuBitableService bitableService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        bitableService = new TestableFeishuBitableService(tokenService);
    }

    @Test
    void testTokenRetrySuccess() throws Exception {
        // 模拟第一次调用失败，第二次成功
        @SuppressWarnings("unchecked")
        Supplier<String> operation = mock(Supplier.class);
        when(operation.get())
                .thenThrow(new TokenExpiredException("Token已过期"))
                .thenReturn("success");

        // 模拟token刷新成功
        when(tokenService.forceRefreshAppAccessToken()).thenReturn("new-token");

        String result = bitableService.testExecuteWithTokenRetry(operation);

        assertEquals("success", result);
        verify(operation, times(2)).get();
        verify(tokenService, times(1)).forceRefreshAppAccessToken();
    }

    @Test
    void testTokenRetryMaxRetriesExceeded() throws Exception {
        // 模拟连续失败
        @SuppressWarnings("unchecked")
        Supplier<String> operation = mock(Supplier.class);
        when(operation.get()).thenThrow(new TokenExpiredException("Token已过期"));

        // 模拟token刷新成功
        when(tokenService.forceRefreshAppAccessToken()).thenReturn("new-token");

        Exception exception = assertThrows(Exception.class, () -> {
            bitableService.testExecuteWithTokenRetry(operation);
        });

        assertTrue(exception.getMessage().contains("已达最大重试次数"));
        verify(operation, times(3)).get(); // 初始调用 + 2次重试
        verify(tokenService, times(2)).forceRefreshAppAccessToken(); // 2次重试
    }

    @Test
    void testTokenRefreshFailure() throws Exception {
        // 模拟token过期
        @SuppressWarnings("unchecked")
        Supplier<String> operation = mock(Supplier.class);
        when(operation.get()).thenThrow(new TokenExpiredException("Token已过期"));

        // 模拟token刷新失败
        when(tokenService.forceRefreshAppAccessToken()).thenThrow(new RuntimeException("刷新失败"));

        Exception exception = assertThrows(Exception.class, () -> {
            bitableService.testExecuteWithTokenRetry(operation);
        });

        assertTrue(exception.getMessage().contains("刷新token失败"));
        verify(operation, times(1)).get(); // 只调用一次，因为刷新失败
        verify(tokenService, times(1)).forceRefreshAppAccessToken();
    }

    @Test
    void testWrappedTokenExpiredException() throws Exception {
        // 模拟包装的TokenExpiredException
        @SuppressWarnings("unchecked")
        Supplier<String> operation = mock(Supplier.class);
        RuntimeException wrappedException = new RuntimeException(new TokenExpiredException("Token已过期"));
        when(operation.get())
                .thenThrow(wrappedException)
                .thenReturn("success");

        // 模拟token刷新成功
        when(tokenService.forceRefreshAppAccessToken()).thenReturn("new-token");

        String result = bitableService.testExecuteWithTokenRetry(operation);

        assertEquals("success", result);
        verify(operation, times(2)).get();
        verify(tokenService, times(1)).forceRefreshAppAccessToken();
    }

    /**
     * 测试用的FeishuBitableService子类，暴露executeWithTokenRetry方法
     */
    private static class TestableFeishuBitableService {
        private final FeishuTokenService tokenService;
        private final Logger logger = LoggerFactory.getLogger(TestableFeishuBitableService.class);

        public TestableFeishuBitableService(FeishuTokenService tokenService) {
            this.tokenService = tokenService;
        }

        public <T> T testExecuteWithTokenRetry(Supplier<T> operation) throws Exception {
            int maxRetries = 2; // 最多重试2次
            int retryCount = 0;
            
            while (retryCount <= maxRetries) {
                try {
                    return operation.get();
                } catch (TokenExpiredException e) {
                    retryCount++;
                    if (retryCount > maxRetries) {
                        logger.error("Token重试次数已达上限 {}, 放弃重试: {}", maxRetries, e.getMessage());
                        throw new Exception("Token过期重试失败，已达最大重试次数: " + maxRetries, e);
                    }
                    
                    logger.warn("Token过期，第{}次尝试刷新token并重试: {}", retryCount, e.getMessage());

                    // 强制刷新token
                    try {
                        tokenService.forceRefreshAppAccessToken();
                        logger.info("Token已刷新，第{}次重试操作", retryCount);
                    } catch (Exception refreshException) {
                        logger.error("刷新token失败: {}", refreshException.getMessage());
                        throw new Exception("刷新token失败", refreshException);
                    }

                    // 添加短暂延迟避免频繁重试
                    if (retryCount > 1) {
                        try {
                            Thread.sleep(100); // 测试中使用较短延迟
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            throw new Exception("重试被中断", ie);
                        }
                    }
                    
                    // 继续下一次循环重试
                    continue;
                    
                } catch (RuntimeException e) {
                    // 检查是否是包装的TokenExpiredException
                    if (e.getCause() instanceof TokenExpiredException) {
                        retryCount++;
                        if (retryCount > maxRetries) {
                            logger.error("Token重试次数已达上限 {}, 放弃重试: {}", maxRetries, e.getCause().getMessage());
                            throw new Exception("Token过期重试失败，已达最大重试次数: " + maxRetries, e.getCause());
                        }
                        
                        logger.warn("检测到包装的Token过期异常，第{}次尝试刷新token并重试: {}", retryCount, e.getCause().getMessage());

                        // 强制刷新token
                        try {
                            tokenService.forceRefreshAppAccessToken();
                            logger.info("Token已刷新，第{}次重试操作", retryCount);
                        } catch (Exception refreshException) {
                            logger.error("刷新token失败: {}", refreshException.getMessage());
                            throw new Exception("刷新token失败", refreshException);
                        }

                        // 添加短暂延迟避免频繁重试
                        if (retryCount > 1) {
                            try {
                                Thread.sleep(100); // 测试中使用较短延迟
                            } catch (InterruptedException ie) {
                                Thread.currentThread().interrupt();
                                throw new Exception("重试被中断", ie);
                            }
                        }
                        
                        // 继续下一次循环重试
                        continue;
                    }
                    
                    // 其他RuntimeException，检查是否包含Exception cause
                    if (e.getCause() instanceof Exception) {
                        throw (Exception) e.getCause();
                    }
                    throw e;
                }
            }
            
            // 理论上不会到达这里
            throw new Exception("未知错误：重试循环异常退出");
        }
    }
}

package com.ziniao.service;

import com.alibaba.fastjson.JSON;
import com.fzzixun.openapi.sdk.client.OpenClient;
import com.fzzixun.openapi.sdk.common.RequestMethod;
import com.fzzixun.openapi.sdk.request.CommonRequest;
import com.fzzixun.openapi.sdk.response.CommonResponse;
import com.ziniao.config.ApiConfig;
import com.ziniao.model.TokenResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

/**
 * 令牌管理服务
 */
@Service
public class TokenService {

    private static final Logger logger = LoggerFactory.getLogger(TokenService.class);

    @Autowired
    private ApiConfig apiConfig;

    // 令牌缓存
    private String cachedToken;
    private LocalDateTime tokenExpireTime;
    private final Object tokenLock = new Object();
    
    private OpenClient openClient;
    
    /**
     * 获取OpenClient实例（懒加载）
     */
    private OpenClient getOpenClient() {
        if (openClient == null) {
            openClient = new OpenClient(
                apiConfig.getBaseUrl(),
                apiConfig.getAppId(),
                apiConfig.getPrivateKey()
            );
        }
        return openClient;
    }
    
    /**
     * 获取应用令牌（智能缓存，自动检查过期）
     *
     * @return 应用令牌
     * @throws Exception 获取失败时抛出异常
     */
    public String getAppToken() throws Exception {
        synchronized (tokenLock) {
            try {
                // 检查缓存的令牌是否有效
                if (isTokenValid()) {
                    logger.debug("使用缓存的令牌");
                    return cachedToken;
                }

                // 令牌无效或过期，重新获取
                logger.info("令牌无效或过期，重新获取应用令牌...");
                return refreshToken();
            } catch (Exception e) {
                logger.error("获取应用令牌失败: {}", e.getMessage());
                // 清除可能损坏的缓存
                cachedToken = null;
                tokenExpireTime = null;
                throw e;
            }
        }
    }

    /**
     * 检查当前缓存的令牌是否有效
     *
     * @return 是否有效
     */
    private boolean isTokenValid() {
        if (cachedToken == null || cachedToken.trim().isEmpty()) {
            logger.debug("缓存令牌为空");
            return false;
        }

        if (tokenExpireTime == null) {
            logger.debug("令牌过期时间未设置");
            return false;
        }

        LocalDateTime now = LocalDateTime.now();
        // 提前5分钟刷新令牌，避免在使用时过期
        LocalDateTime refreshTime = tokenExpireTime.minus(5, ChronoUnit.MINUTES);

        if (now.isAfter(refreshTime)) {
            logger.info("令牌即将过期或已过期，当前时间: {}, 过期时间: {}, 刷新时间: {}",
                       now, tokenExpireTime, refreshTime);
            return false;
        }

        logger.debug("令牌有效，过期时间: {}", tokenExpireTime);
        return true;
    }

    /**
     * 刷新令牌
     *
     * @return 新的令牌
     * @throws Exception 获取失败时抛出异常
     */
    public String refreshToken() throws Exception {
        String apiPath = "/auth/get_app_token";
        CommonRequest request = new CommonRequest(apiPath, RequestMethod.POST_JSON);

        // 打印等效的curl命令
        printTokenCurlCommand(apiPath);

        try {
            CommonResponse response = getOpenClient().execute(request);

            if (response.isSuccess()) {
                String data = response.getData();
                logger.info("获取令牌响应: {}", data);

                TokenResponse tokenResponse = JSON.parseObject(data, TokenResponse.class);

                if (tokenResponse != null && tokenResponse.isValid()) {
                    // 缓存新令牌
                    cachedToken = tokenResponse.getAppAuthToken();

                    // 计算过期时间（从当前时间开始计算）
                    Long expiresIn = tokenResponse.getExpiresIn(); // 秒数
                    if (expiresIn != null && expiresIn > 0) {
                        tokenExpireTime = LocalDateTime.now().plus(expiresIn, ChronoUnit.SECONDS);
                        logger.info("成功获取应用令牌，过期时间: {}", tokenExpireTime);
                    } else {
                        // 如果没有过期时间，默认设置为2小时
                        tokenExpireTime = LocalDateTime.now().plus(2, ChronoUnit.HOURS);
                        logger.warn("令牌响应中没有过期时间，默认设置为2小时后过期: {}", tokenExpireTime);
                    }

                    return cachedToken;
                } else {
                    throw new RuntimeException("令牌响应无效: " + data);
                }

            } else {
                logger.error("获取令牌失败: {}", response.getErrorMsg());
                throw new RuntimeException("获取令牌失败: " + response.getErrorMsg());
            }

        } catch (Exception e) {
            logger.error("获取令牌异常", e);
            throw e;
        }
    }
    
    /**
     * 强制刷新令牌（清除缓存并重新获取）
     *
     * @return 新的应用令牌
     * @throws Exception 获取失败时抛出异常
     */
    public String forceRefreshToken() throws Exception {
        synchronized (tokenLock) {
            logger.info("强制刷新应用令牌，清除缓存...");

            // 记录旧令牌信息用于调试
            if (cachedToken != null) {
                logger.debug("清除旧令牌，过期时间: {}", tokenExpireTime);
            }

            // 清除缓存
            cachedToken = null;
            tokenExpireTime = null;

            try {
                // 重新获取
                String newToken = refreshToken();
                logger.info("强制刷新令牌成功，新令牌过期时间: {}", tokenExpireTime);
                return newToken;
            } catch (Exception e) {
                logger.error("强制刷新令牌失败: {}", e.getMessage());
                throw e;
            }
        }
    }
    
    /**
     * 验证令牌是否有效
     *
     * @param token 要验证的令牌
     * @return 是否有效
     */
    public boolean validateToken(String token) {
        return token != null && !token.trim().isEmpty();
    }

    /**
     * 生成并打印获取令牌的curl命令
     *
     * @param apiPath API路径
     */
    private void printTokenCurlCommand(String apiPath) {
        try {
            String baseUrl = apiConfig.getBaseUrl();
            String fullUrl = baseUrl + apiPath;

            // 构建curl命令
            StringBuilder curlCommand = new StringBuilder();
            curlCommand.append("curl -X POST \"").append(fullUrl).append("\" \\\n");
            curlCommand.append("  -H \"Content-Type: application/json\" \\\n");
            curlCommand.append("  -H \"app-id: ").append(apiConfig.getAppId()).append("\" \\\n");
            curlCommand.append("  -H \"private-key: [PRIVATE_KEY]\" \\\n");
            curlCommand.append("  -d '{}'");

            logger.info("等效的获取令牌curl命令:\n{}", curlCommand.toString());

            // 也打印带实际私钥的版本（仅用于调试，生产环境应隐藏）
            StringBuilder realCurl = new StringBuilder();
            realCurl.append("curl -X POST \"").append(fullUrl).append("\" \\\n");
            realCurl.append("  -H \"Content-Type: application/json\" \\\n");
            realCurl.append("  -H \"app-id: ").append(apiConfig.getAppId()).append("\" \\\n");
            realCurl.append("  -H \"private-key: ").append(apiConfig.getPrivateKey()).append("\" \\\n");
            realCurl.append("  -d '{}'");

            logger.debug("完整的获取令牌curl命令（包含私钥）:\n{}", realCurl.toString());

        } catch (Exception e) {
            logger.warn("生成令牌curl命令失败", e);
        }
    }
}
